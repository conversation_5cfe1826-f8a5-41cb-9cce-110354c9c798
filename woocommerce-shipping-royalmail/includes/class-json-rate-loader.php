<?php
/**
 * JSON Rate Loader Service.
 *
 * @package WC_RoyalMail
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * JSON Rate Loader class.
 *
 * Handles loading and caching of Royal Mail rate data from CDN endpoints.
 * Provides methods to fetch, validate, and cache rate information for different
 * Royal Mail services and rate types.
 *
 */
class WC_RoyalMail_JSON_Rate_Loader {
	/**
	 * Default cache expiration time in seconds (1 week by default).
	 *
	 * @var int
	 */
	const CACHE_EXPIRATION = WEEK_IN_SECONDS;

	/**
	 * Cache key prefix for WordPress transients.
	 *
	 * @var string
	 */
	const CACHE_PREFIX = 'royalmail_rates_';

	/**
	 * CDN URL for rate paths configuration.
	 *
	 * @var string
	 */
	const RATE_PATHS_URL = 'https://progressus.lvendr.com/royalmail/rates/get-rates.json';

	/**
	 * CDN URLs configuration for different rate types.
	 * Populated dynamically from CDN configuration.
	 *
	 * @var array
	 */
	private static array $cdn_urls = array();

	/**
	 * Database table name for storing rates.
	 *
	 * @var string
	 */
	private static string $table_name = '';

	/**
	 * Get the database table name.
	 *
	 * @return string Database table name.
	 */
	private static function get_table_name(): string {
		if ( empty( self::$table_name ) ) {
			global $wpdb;
			self::$table_name = $wpdb->prefix . 'royalmail_rates';
		}

		return self::$table_name;
	}

	/**
	 * Initialize CDN URLs from configuration.
	 *
	 * @return bool True if URLs loaded successfully, false otherwise.
	 */
	private static function init_cdn_urls(): bool {
		if ( ! empty( self::$cdn_urls ) ) {
			return true;
		}

		// Fetch from CDN.
		$response = wp_remote_get(
			self::RATE_PATHS_URL,
			array(
				'timeout' => 30,
				'headers' => array(
					'Accept'     => 'application/json',
					'User-Agent' => 'WC_RoyalMail/' . WOOCOMMERCE_SHIPPING_ROYALMAIL_VERSION . '; ' . home_url( '/' ),
				),
			)
		);

		if ( is_wp_error( $response ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
			return false;
		}

		$data = json_decode( wp_remote_retrieve_body( $response ), true );
		if ( ! isset( $data['rates-paths'] ) || ! is_array( $data['rates-paths'] ) ) {
			return false;
		}

		// Find current valid rates.
		$current_date = current_time( 'Y-m-d' );
		$current_urls = null;

		foreach ( $data['rates-paths'] as $start_date => $urls ) {
			if ( $start_date <= $current_date ) {
				$current_urls = $urls;
			}
		}

		if ( null === $current_urls ) {
			return false;
		}

		self::$cdn_urls = $current_urls;

		// Check if we need to sync rates to database.
		if ( self::needs_sync() ) {
			// Schedule a background sync to avoid blocking the current request.
			wp_schedule_single_event( time() + 60, 'royalmail_sync_rates' );
		}

		return true;
	}

	/**
	 * Load rate data for a specific service.
	 *
	 * Attempts to load rate data from database first, then from CDN if not available.
	 * Saves successful CDN responses to database for future use.
	 *
	 * @param string $service_slug Service slug (e.g., 'international-economy').
	 * @param string $rate_type    Rate type ('online' or 'regular').
	 *
	 * @return array|false Rate data array or false on failure.
	 *
	 */
	public static function load_rate_data( string $service_slug, string $rate_type ) {
		// Sanitize and validate input parameters.
		$service_slug = sanitize_key( $service_slug );
		$rate_type    = sanitize_key( $rate_type );

		// Create service code by combining service slug and rate type.
		$service_code = $service_slug . '_' . $rate_type;

		// Try to get rate data from database first.
		$rate_data = self::get_rate_from_database( $service_code );

		if ( false !== $rate_data ) {
			return $rate_data;
		}

		// If not in database, fetch from CDN.
		$rate_data = self::fetch_from_cdn( $service_slug, $rate_type );

		if ( false !== $rate_data ) {
			// Save to database for future use.
			self::save_rate_to_database( $service_code, $rate_data );
		}

		return $rate_data;
	}

	/**
	 * Fetch rate data from CDN.
	 *
	 * Makes an HTTP request to the CDN endpoint to retrieve rate data
	 * for the specified service and rate type.
	 *
	 * @param string $service_slug Service slug.
	 * @param string $rate_type    Rate type.
	 *
	 * @return array|false Rate data or false on failure.
	 */
	private static function fetch_from_cdn( string $service_slug, string $rate_type ) {
		if ( ! self::init_cdn_urls() ) {
			return false;
		}

		if ( empty( self::$cdn_urls[ $rate_type ] ) ) {
			return false;
		}

		$base_url = trailingslashit( self::$cdn_urls[ $rate_type ] );
		$json_url = $base_url . $service_slug . '.json';

		$response = wp_remote_get(
			$json_url,
			array(
				'timeout' => 30,
				'headers' => array(
					'Accept'     => 'application/json',
					'User-Agent' => 'WC_RoyalMail/' . WOOCOMMERCE_SHIPPING_ROYALMAIL_VERSION . '; ' . home_url( '/' ),
				),
			)
		);

		if ( is_wp_error( $response ) ) {
			return false;
		}

		$response_code = wp_remote_retrieve_response_code( $response );
		if ( 200 !== $response_code ) {
			return false;
		}

		$body = wp_remote_retrieve_body( $response );
		$data = json_decode( $body, true );

		return self::validate_rate_data( $data );
	}

	/**
	 * Validate rate data structure.
	 *
	 * Ensures the rate data contains required fields and at least one
	 * valid rate structure (packages, zones, or compensation levels).
	 *
	 * @param mixed $data Rate data to validate.
	 *
	 * @return array|false Validated data or false on failure.
	 */
	private static function validate_rate_data( $data ) {
		// Ensure data is an array.
		if ( ! is_array( $data ) ) {
			error_log( 'Rate data is not an array' );
			return false;
		}

		// Check required fields.
		$required_fields = array( 'service', 'year' );
		foreach ( $required_fields as $field ) {
			if ( ! isset( $data[ $field ] ) ) {
				error_log( 'Missing required field: ' . sanitize_text_field( $field ) );
				return false;
			}
		}

		// Validate that we have at least one rate structure.
		$has_rate_data = false;
		if ( ! empty( $data['packages'] ) && is_array( $data['packages'] ) ) {
			$has_rate_data = true;
		} elseif ( ! empty( $data['zones'] ) && is_array( $data['zones'] ) ) {
			$has_rate_data = true;
		} elseif ( ! empty( $data['compensation'] ) && is_array( $data['compensation'] ) ) {
			$has_rate_data = true;
		}

		if ( ! $has_rate_data ) {
			return false;
		}

		return $data;
	}

	/**
	 * Get rate data from database for a specific service.
	 *
	 * @param string $service_code Service code (e.g., 'international-economy_regular').
	 *
	 * @return array|false Rate data array or false if not found.
	 */
	private static function get_rate_from_database( string $service_code ) {
		global $wpdb;

		$table_name = self::get_table_name();

		// Get current rate data based on start_date.
		$result = $wpdb->get_row(
			$wpdb->prepare(
				"SELECT rates_json FROM {$table_name}
				WHERE service_code = %s
				AND start_date <= CURDATE()
				ORDER BY start_date DESC
				LIMIT 1",
				$service_code
			)
		);

		if ( null === $result ) {
			return false;
		}

		$rate_data = json_decode( $result->rates_json, true );

		return self::validate_rate_data( $rate_data );
	}

	/**
	 * Save rate data to database.
	 *
	 * @param string $service_code Service code (e.g., 'international-economy_regular').
	 * @param array  $rate_data    Rate data to save.
	 * @param string $start_date   Start date for the rates (defaults to current date).
	 *
	 * @return bool True on success, false on failure.
	 */
	private static function save_rate_to_database( string $service_code, array $rate_data, string $start_date = '' ): bool {
		global $wpdb;

		if ( empty( $start_date ) ) {
			$start_date = current_time( 'Y-m-d' );
		}

		$table_name = self::get_table_name();

		// Check if this exact rate already exists.
		$existing = $wpdb->get_var(
			$wpdb->prepare(
				"SELECT id FROM {$table_name}
				WHERE service_code = %s
				AND start_date = %s",
				$service_code,
				$start_date
			)
		);

		$rates_json = wp_json_encode( $rate_data );

		if ( $existing ) {
			// Update existing record.
			$result = $wpdb->update(
				$table_name,
				array(
					'rates_json' => $rates_json,
				),
				array(
					'id' => $existing,
				),
				array( '%s' ),
				array( '%d' )
			);
		} else {
			// Insert new record.
			$result = $wpdb->insert(
				$table_name,
				array(
					'service_code' => $service_code,
					'start_date'   => $start_date,
					'rates_json'   => $rates_json,
				),
				array( '%s', '%s', '%s' )
			);
		}

		return false !== $result;
	}

	/**
	 * Sync all available rates from CDN to database.
	 *
	 * This method fetches all available services and rate types from the CDN
	 * and saves them to the database with the appropriate start dates.
	 *
	 * @return array Results array with success/failure counts.
	 */
	public static function sync_rates_to_database(): array {
		$results = array(
			'success' => 0,
			'failed'  => 0,
			'errors'  => array(),
		);

		// Get all available services from the Services class.
		$all_services = self::get_all_service_codes();

		if ( empty( $all_services ) ) {
			$results['errors'][] = 'No services found to sync.';
			return $results;
		}

		// Get rate paths configuration to determine start dates.
		$rate_paths_data = self::get_rate_paths_data();
		if ( false === $rate_paths_data ) {
			$results['errors'][] = 'Failed to fetch rate paths configuration.';
			return $results;
		}

		$rate_types = array( 'regular', 'online' );

		foreach ( $rate_paths_data['rates-paths'] as $start_date => $paths ) {
			foreach ( $rate_types as $rate_type ) {
				foreach ( $all_services as $service_slug ) {
					$service_code = $service_slug . '_' . $rate_type;

					// Fetch rate data from CDN.
					$rate_data = self::fetch_from_cdn( $service_slug, $rate_type );

					if ( false !== $rate_data ) {
						$saved = self::save_rate_to_database( $service_code, $rate_data, $start_date );
						if ( $saved ) {
							$results['success']++;
						} else {
							$results['failed']++;
							$results['errors'][] = "Failed to save {$service_code} for {$start_date}";
						}
					} else {
						$results['failed']++;
						$results['errors'][] = "Failed to fetch {$service_code} from CDN";
					}
				}
			}
		}

		return $results;
	}

	/**
	 * Get rate paths data from CDN.
	 *
	 * @return array|false Rate paths data or false on failure.
	 */
	private static function get_rate_paths_data() {
		$response = wp_remote_get(
			self::RATE_PATHS_URL,
			array(
				'timeout' => 30,
				'headers' => array(
					'Accept'     => 'application/json',
					'User-Agent' => 'WC_RoyalMail/' . WOOCOMMERCE_SHIPPING_ROYALMAIL_VERSION . '; ' . home_url( '/' ),
				),
			)
		);

		if ( is_wp_error( $response ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
			return false;
		}

		$data = json_decode( wp_remote_retrieve_body( $response ), true );
		if ( ! isset( $data['rates-paths'] ) || ! is_array( $data['rates-paths'] ) ) {
			return false;
		}

		return $data;
	}

	/**
	 * Get all service codes from the Services class.
	 *
	 * @return array Array of service codes.
	 */
	private static function get_all_service_codes(): array {
		if ( ! class_exists( 'WooCommerce\RoyalMail\Services' ) ) {
			return array();
		}

		$reflection = new ReflectionClass( 'WooCommerce\RoyalMail\Services' );
		$constants  = $reflection->getConstants();

		return array_values( $constants );
	}

	/**
	 * Clean up old rate data from database.
	 *
	 * Removes rate data older than the specified number of days.
	 *
	 * @param int $days_to_keep Number of days to keep (default: 90).
	 *
	 * @return int Number of rows deleted.
	 */
	public static function cleanup_old_rates( int $days_to_keep = 90 ): int {
		global $wpdb;

		$table_name = self::get_table_name();

		$cutoff_date = gmdate( 'Y-m-d', strtotime( "-{$days_to_keep} days" ) );

		$deleted = $wpdb->query(
			$wpdb->prepare(
				"DELETE FROM {$table_name} WHERE start_date < %s",
				$cutoff_date
			)
		);

		return (int) $deleted;
	}

	/**
	 * Check if rates need to be synced from CDN.
	 *
	 * @return bool True if sync is needed, false otherwise.
	 */
	public static function needs_sync(): bool {
		// Get the latest update date from CDN.
		$rate_paths_data = self::get_rate_paths_data();
		if ( false === $rate_paths_data ) {
			return false;
		}

		$latest_cdn_date = $rate_paths_data['latest-update'] ?? '';
		if ( empty( $latest_cdn_date ) ) {
			return false;
		}

		// Check if we have any rates for this date in the database.
		global $wpdb;
		$table_name = self::get_table_name();

		$count = $wpdb->get_var(
			$wpdb->prepare(
				"SELECT COUNT(*) FROM {$table_name} WHERE start_date = %s",
				$latest_cdn_date
			)
		);

		return 0 === (int) $count;
	}

	/**
	 * Force sync all rates from CDN to database.
	 *
	 * This is a public method that can be called to manually trigger a sync.
	 *
	 * @return array Results array with success/failure counts.
	 */
	public static function force_sync_rates(): array {
		return self::sync_rates_to_database();
	}
}
