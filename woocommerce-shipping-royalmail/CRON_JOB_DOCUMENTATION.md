# Royal Mail Cron Job Documentation

## Overview

The WooCommerce Royal Mail plugin now includes an automated cron job that runs daily at midnight to check for rate updates and sync them to the database if needed.

## How It Works

### 1. Cron Job Scheduling

The cron job is automatically scheduled when the plugin is loaded:

- **Hook Name**: `wc_royalmail_daily_rate_sync`
- **Schedule**: Daily at midnight
- **Function**: `WC_RoyalMail::daily_rate_sync_callback()`

### 2. Sync Process

The daily cron job follows this process:

1. **Check if sync is needed**: Calls `WC_RoyalMail_JSON_Rate_Loader::needs_sync()`
   - Compares the latest update date from the CDN with the last sync date stored locally
   - Returns `true` if CDN has newer data

2. **Perform sync if needed**: Calls `WC_RoyalMail_JSON_Rate_Loader::sync_rates_to_database()`
   - Fetches all available rate data from the CDN
   - Saves the data to the local database
   - Updates the last sync date

3. **Log results**: Records success/failure information in the error log

### 3. Implementation Details

#### Files Modified

1. **`includes/class-wc-royalmail.php`**
   - Added cron job scheduling in constructor
   - Added `schedule_rate_sync_cron()` method
   - Added `unschedule_rate_sync_cron()` method
   - Added `daily_rate_sync_callback()` method

2. **`includes/class-json-rate-loader.php`**
   - Fixed bug in `needs_sync()` method (was using `update_option()` instead of `get_option()`)

#### New Methods

```php
// Schedule the daily cron job
public function schedule_rate_sync_cron()

// Remove the cron job (called on plugin deactivation)
public function unschedule_rate_sync_cron()

// The actual cron job callback
public function daily_rate_sync_callback()
```

### 4. Manual Testing

You can manually trigger the sync process using the existing admin interface or by calling the methods directly:

```php
// Check if sync is needed
$needs_sync = WC_RoyalMail_JSON_Rate_Loader::needs_sync();

// Manually sync rates
$results = WC_RoyalMail_JSON_Rate_Loader::sync_rates_to_database();
```

### 5. Monitoring

The cron job logs its activity to the WordPress error log:

- Success: "WC Royal Mail: Daily rate sync completed successfully. Synced: X, Failed: Y"
- Errors: "WC Royal Mail: Daily rate sync completed with errors: [error details]"

### 6. WordPress Cron System

The implementation uses WordPress's built-in cron system (`wp_cron`):

- Cron jobs are triggered by website visits
- For low-traffic sites, consider setting up a real cron job to hit `wp-cron.php`
- The cron job will only run if the site receives traffic around midnight

### 7. Cleanup

The cron job is automatically removed when the plugin is deactivated to prevent orphaned scheduled events.

## Benefits

1. **Automatic Updates**: Rate data stays current without manual intervention
2. **Efficient**: Only syncs when new data is available
3. **Reliable**: Uses WordPress's built-in cron system
4. **Logged**: All sync activities are recorded for monitoring
5. **Clean**: Properly cleaned up on plugin deactivation

## Troubleshooting

If the cron job isn't running:

1. Check if WordPress cron is working: `wp cron event list` (if WP-CLI is available)
2. Ensure the site receives traffic around midnight
3. Check error logs for any sync failures
4. Verify the cron job is scheduled: `wp_next_scheduled('wc_royalmail_daily_rate_sync')`
