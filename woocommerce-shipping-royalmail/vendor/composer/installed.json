{"packages": [{"name": "automattic/jetpack-autoloader", "version": "v3.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-autoloader.git", "reference": "e7e49a4e2f16cb2dfd3e58c478499a60d7d51839"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-autoloader/zipball/e7e49a4e2f16cb2dfd3e58c478499a60d7d51839", "reference": "e7e49a4e2f16cb2dfd3e58c478499a60d7d51839", "shasum": ""}, "require": {"composer-plugin-api": "^1.1 || ^2.0", "php": ">=7.0"}, "require-dev": {"automattic/jetpack-changelogger": "^4.2.8", "composer/composer": "^1.1 || ^2.0", "yoast/phpunit-polyfills": "^1.1.1"}, "time": "2024-11-04T09:23:56+00:00", "type": "composer-plugin", "extra": {"class": "Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin", "autotagger": true, "mirror-repo": "Automattic/jetpack-autoloader", "branch-alias": {"dev-trunk": "3.1.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-autoloader/compare/v${old}...v${new}"}, "version-constants": {"::VERSION": "src/AutoloadGenerator.php"}}, "installation-source": "dist", "autoload": {"psr-4": {"Automattic\\Jetpack\\Autoloader\\": "src"}, "classmap": ["src/AutoloadGenerator.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Creates a custom autoloader for a plugin or theme.", "keywords": ["autoload", "autoloader", "composer", "jetpack", "plugin", "wordpress"], "support": {"source": "https://github.com/Automattic/jetpack-autoloader/tree/v3.1.3"}, "install-path": "../automattic/jetpack-autoloader"}, {"name": "composer/installers", "version": "v1.12.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "d20a64ed3c94748397ff5973488761b22f6d3f19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/d20a64ed3c94748397ff5973488761b22f6d3f19", "reference": "d20a64ed3c94748397ff5973488761b22f6d3f19", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0"}, "replace": {"roundcube/plugin-installer": "*", "shama/baton": "*"}, "require-dev": {"composer/composer": "1.6.* || ^2.0", "composer/semver": "^1 || ^3", "phpstan/phpstan": "^0.12.55", "phpstan/phpstan-phpunit": "^0.12.16", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.3"}, "time": "2021-09-13T08:19:44+00:00", "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["Craft", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "aimeos", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "j<PERSON><PERSON>", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "symfony", "tastyigniter", "typo3", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v1.12.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./installers"}, {"name": "dvdoug/boxpacker", "version": "3.9.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dvdoug/BoxPacker.git", "reference": "b9063023729c403e3c20a7635663f29fac569c96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dvdoug/BoxPacker/zipball/b9063023729c403e3c20a7635663f29fac569c96", "reference": "b9063023729c403e3c20a7635663f29fac569c96", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1||^8.0", "psr/log": "^1.0"}, "require-dev": {"behat/behat": "^3.7", "dvdoug/behat-code-coverage": "^5.0.1", "friendsofphp/php-cs-fixer": "^3.0", "monolog/monolog": "^1.0||^2.0", "phpunit/phpunit": "^7.5.20||^8.5.21||^9.5.8"}, "time": "2021-09-26T19:22:28+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"DVDoug\\BoxPacker\\": "src/", "DVDoug\\BoxPacker\\Test\\": "tests/Test"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "role": "Developer"}], "description": "An implementation of the 3D (actually 4D) bin packing/knapsack problem (aka creating parcels by putting items into boxes)", "homepage": "http://boxpacker.io/", "keywords": ["bin packing", "binpacking", "box", "boxes", "boxpacking", "container", "knapsack", "packaging", "packing", "parcel", "parcelpacking", "shipping"], "support": {"issues": "https://github.com/dvdoug/BoxPacker/issues", "source": "https://github.com/dvdoug/BoxPacker/tree/3.9.3"}, "funding": [{"url": "https://github.com/dvdoug", "type": "github"}], "install-path": "../dvdoug/boxpacker"}, {"name": "psr/log", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2021-05-03T11:20:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "install-path": "../psr/log"}, {"name": "woocommerce/box-packer", "version": "1.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "**************:woocommerce/box-packer.git", "reference": "189c4f91d164516ab38228ea0718608f1940d495"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/woocommerce/box-packer/zipball/189c4f91d164516ab38228ea0718608f1940d495", "reference": "189c4f91d164516ab38228ea0718608f1940d495", "shasum": ""}, "require": {"dvdoug/boxpacker": "3.9.3"}, "require-dev": {"automattic/jetpack-autoloader": "^3", "phpunit/phpunit": "9.5"}, "time": "2024-11-05T11:26:27+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"WooCommerce\\BoxPacker\\": "./src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://gedex.web.id"}], "description": "Box packer library for WooCommerce shipping", "homepage": "https://github.com/woocommerce/box-packer", "support": {"source": "https://github.com/woocommerce/box-packer/tree/1.2.0", "issues": "https://github.com/woocommerce/box-packer/issues"}, "install-path": "../woocommerce/box-packer"}], "dev": false, "dev-package-names": []}