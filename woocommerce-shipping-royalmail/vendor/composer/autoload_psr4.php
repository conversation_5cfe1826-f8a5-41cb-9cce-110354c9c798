<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'WooCommerce\\BoxPacker\\' => array($vendorDir . '/woocommerce/box-packer/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
    'DVDoug\\BoxPacker\\Test\\' => array($vendorDir . '/dvdoug/boxpacker/tests/Test'),
    'DVDoug\\BoxPacker\\' => array($vendorDir . '/dvdoug/boxpacker/src'),
    'Composer\\Installers\\' => array($vendorDir . '/composer/installers/src/Composer/Installers'),
    'Automattic\\Jetpack\\Autoloader\\' => array($vendorDir . '/automattic/jetpack-autoloader/src'),
);
