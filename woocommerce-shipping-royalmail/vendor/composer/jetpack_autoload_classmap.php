<?php

// This file `jetpack_autoload_classmap.php` was auto generated by automattic/jetpack-autoloader.

$vendorDir = dirname(__DIR__);
$baseDir   = dirname($vendorDir);

return array(
	'Autoloader' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader.php'
	),
	'Autoloader_Handler' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader-handler.php'
	),
	'Autoloader_Locator' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader-locator.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadFileWriter' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadFileWriter.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadGenerator' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadGenerator.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadProcessor' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadProcessor.php'
	),
	'Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/CustomAutoloaderPlugin.php'
	),
	'Automattic\\Jetpack\\Autoloader\\ManifestGenerator' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/ManifestGenerator.php'
	),
	'Composer\\Installers\\AglInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AglInstaller.php'
	),
	'Composer\\Installers\\AimeosInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AimeosInstaller.php'
	),
	'Composer\\Installers\\AnnotateCmsInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AnnotateCmsInstaller.php'
	),
	'Composer\\Installers\\AsgardInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AsgardInstaller.php'
	),
	'Composer\\Installers\\AttogramInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AttogramInstaller.php'
	),
	'Composer\\Installers\\BaseInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/BaseInstaller.php'
	),
	'Composer\\Installers\\BitrixInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/BitrixInstaller.php'
	),
	'Composer\\Installers\\BonefishInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/BonefishInstaller.php'
	),
	'Composer\\Installers\\CakePHPInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CakePHPInstaller.php'
	),
	'Composer\\Installers\\ChefInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ChefInstaller.php'
	),
	'Composer\\Installers\\CiviCrmInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CiviCrmInstaller.php'
	),
	'Composer\\Installers\\ClanCatsFrameworkInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ClanCatsFrameworkInstaller.php'
	),
	'Composer\\Installers\\CockpitInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CockpitInstaller.php'
	),
	'Composer\\Installers\\CodeIgniterInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CodeIgniterInstaller.php'
	),
	'Composer\\Installers\\Concrete5Installer' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Concrete5Installer.php'
	),
	'Composer\\Installers\\CraftInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CraftInstaller.php'
	),
	'Composer\\Installers\\CroogoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CroogoInstaller.php'
	),
	'Composer\\Installers\\DecibelInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DecibelInstaller.php'
	),
	'Composer\\Installers\\DframeInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DframeInstaller.php'
	),
	'Composer\\Installers\\DokuWikiInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DokuWikiInstaller.php'
	),
	'Composer\\Installers\\DolibarrInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DolibarrInstaller.php'
	),
	'Composer\\Installers\\DrupalInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DrupalInstaller.php'
	),
	'Composer\\Installers\\ElggInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ElggInstaller.php'
	),
	'Composer\\Installers\\EliasisInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/EliasisInstaller.php'
	),
	'Composer\\Installers\\ExpressionEngineInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ExpressionEngineInstaller.php'
	),
	'Composer\\Installers\\EzPlatformInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/EzPlatformInstaller.php'
	),
	'Composer\\Installers\\FuelInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/FuelInstaller.php'
	),
	'Composer\\Installers\\FuelphpInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/FuelphpInstaller.php'
	),
	'Composer\\Installers\\GravInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/GravInstaller.php'
	),
	'Composer\\Installers\\HuradInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/HuradInstaller.php'
	),
	'Composer\\Installers\\ImageCMSInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ImageCMSInstaller.php'
	),
	'Composer\\Installers\\Installer' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Installer.php'
	),
	'Composer\\Installers\\ItopInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ItopInstaller.php'
	),
	'Composer\\Installers\\JoomlaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/JoomlaInstaller.php'
	),
	'Composer\\Installers\\KanboardInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KanboardInstaller.php'
	),
	'Composer\\Installers\\KirbyInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KirbyInstaller.php'
	),
	'Composer\\Installers\\KnownInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KnownInstaller.php'
	),
	'Composer\\Installers\\KodiCMSInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KodiCMSInstaller.php'
	),
	'Composer\\Installers\\KohanaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KohanaInstaller.php'
	),
	'Composer\\Installers\\LanManagementSystemInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/LanManagementSystemInstaller.php'
	),
	'Composer\\Installers\\LaravelInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/LaravelInstaller.php'
	),
	'Composer\\Installers\\LavaLiteInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/LavaLiteInstaller.php'
	),
	'Composer\\Installers\\LithiumInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/LithiumInstaller.php'
	),
	'Composer\\Installers\\MODULEWorkInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MODULEWorkInstaller.php'
	),
	'Composer\\Installers\\MODXEvoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MODXEvoInstaller.php'
	),
	'Composer\\Installers\\MagentoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MagentoInstaller.php'
	),
	'Composer\\Installers\\MajimaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MajimaInstaller.php'
	),
	'Composer\\Installers\\MakoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MakoInstaller.php'
	),
	'Composer\\Installers\\MantisBTInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MantisBTInstaller.php'
	),
	'Composer\\Installers\\MauticInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MauticInstaller.php'
	),
	'Composer\\Installers\\MayaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MayaInstaller.php'
	),
	'Composer\\Installers\\MediaWikiInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MediaWikiInstaller.php'
	),
	'Composer\\Installers\\MiaoxingInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MiaoxingInstaller.php'
	),
	'Composer\\Installers\\MicroweberInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MicroweberInstaller.php'
	),
	'Composer\\Installers\\ModxInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ModxInstaller.php'
	),
	'Composer\\Installers\\MoodleInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MoodleInstaller.php'
	),
	'Composer\\Installers\\OctoberInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/OctoberInstaller.php'
	),
	'Composer\\Installers\\OntoWikiInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/OntoWikiInstaller.php'
	),
	'Composer\\Installers\\OsclassInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/OsclassInstaller.php'
	),
	'Composer\\Installers\\OxidInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/OxidInstaller.php'
	),
	'Composer\\Installers\\PPIInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PPIInstaller.php'
	),
	'Composer\\Installers\\PantheonInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PantheonInstaller.php'
	),
	'Composer\\Installers\\PhiftyInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PhiftyInstaller.php'
	),
	'Composer\\Installers\\PhpBBInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PhpBBInstaller.php'
	),
	'Composer\\Installers\\PimcoreInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PimcoreInstaller.php'
	),
	'Composer\\Installers\\PiwikInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PiwikInstaller.php'
	),
	'Composer\\Installers\\PlentymarketsInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PlentymarketsInstaller.php'
	),
	'Composer\\Installers\\Plugin' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Plugin.php'
	),
	'Composer\\Installers\\PortoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PortoInstaller.php'
	),
	'Composer\\Installers\\PrestashopInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PrestashopInstaller.php'
	),
	'Composer\\Installers\\ProcessWireInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ProcessWireInstaller.php'
	),
	'Composer\\Installers\\PuppetInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PuppetInstaller.php'
	),
	'Composer\\Installers\\PxcmsInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PxcmsInstaller.php'
	),
	'Composer\\Installers\\RadPHPInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/RadPHPInstaller.php'
	),
	'Composer\\Installers\\ReIndexInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ReIndexInstaller.php'
	),
	'Composer\\Installers\\Redaxo5Installer' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Redaxo5Installer.php'
	),
	'Composer\\Installers\\RedaxoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/RedaxoInstaller.php'
	),
	'Composer\\Installers\\RoundcubeInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/RoundcubeInstaller.php'
	),
	'Composer\\Installers\\SMFInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SMFInstaller.php'
	),
	'Composer\\Installers\\ShopwareInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ShopwareInstaller.php'
	),
	'Composer\\Installers\\SilverStripeInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SilverStripeInstaller.php'
	),
	'Composer\\Installers\\SiteDirectInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SiteDirectInstaller.php'
	),
	'Composer\\Installers\\StarbugInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/StarbugInstaller.php'
	),
	'Composer\\Installers\\SyDESInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SyDESInstaller.php'
	),
	'Composer\\Installers\\SyliusInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SyliusInstaller.php'
	),
	'Composer\\Installers\\Symfony1Installer' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Symfony1Installer.php'
	),
	'Composer\\Installers\\TYPO3CmsInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3CmsInstaller.php'
	),
	'Composer\\Installers\\TYPO3FlowInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3FlowInstaller.php'
	),
	'Composer\\Installers\\TaoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TaoInstaller.php'
	),
	'Composer\\Installers\\TastyIgniterInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TastyIgniterInstaller.php'
	),
	'Composer\\Installers\\TheliaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TheliaInstaller.php'
	),
	'Composer\\Installers\\TuskInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TuskInstaller.php'
	),
	'Composer\\Installers\\UserFrostingInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/UserFrostingInstaller.php'
	),
	'Composer\\Installers\\VanillaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/VanillaInstaller.php'
	),
	'Composer\\Installers\\VgmcpInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/VgmcpInstaller.php'
	),
	'Composer\\Installers\\WHMCSInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/WHMCSInstaller.php'
	),
	'Composer\\Installers\\WinterInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/WinterInstaller.php'
	),
	'Composer\\Installers\\WolfCMSInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/WolfCMSInstaller.php'
	),
	'Composer\\Installers\\WordPressInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/WordPressInstaller.php'
	),
	'Composer\\Installers\\YawikInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/YawikInstaller.php'
	),
	'Composer\\Installers\\ZendInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ZendInstaller.php'
	),
	'Composer\\Installers\\ZikulaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ZikulaInstaller.php'
	),
	'Container' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-container.php'
	),
	'DVDoug\\BoxPacker\\Box' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/Box.php'
	),
	'DVDoug\\BoxPacker\\BoxList' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/BoxList.php'
	),
	'DVDoug\\BoxPacker\\ConstrainedItem' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/ConstrainedItem.php'
	),
	'DVDoug\\BoxPacker\\ConstrainedPlacementItem' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/ConstrainedPlacementItem.php'
	),
	'DVDoug\\BoxPacker\\InfalliblePacker' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/InfalliblePacker.php'
	),
	'DVDoug\\BoxPacker\\Item' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/Item.php'
	),
	'DVDoug\\BoxPacker\\ItemList' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/ItemList.php'
	),
	'DVDoug\\BoxPacker\\ItemTooLargeException' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/ItemTooLargeException.php'
	),
	'DVDoug\\BoxPacker\\LayerPacker' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/LayerPacker.php'
	),
	'DVDoug\\BoxPacker\\LayerStabiliser' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/LayerStabiliser.php'
	),
	'DVDoug\\BoxPacker\\LimitedSupplyBox' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/LimitedSupplyBox.php'
	),
	'DVDoug\\BoxPacker\\NoBoxesAvailableException' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/NoBoxesAvailableException.php'
	),
	'DVDoug\\BoxPacker\\OrientatedItem' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/OrientatedItem.php'
	),
	'DVDoug\\BoxPacker\\OrientatedItemFactory' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/OrientatedItemFactory.php'
	),
	'DVDoug\\BoxPacker\\OrientatedItemSorter' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/OrientatedItemSorter.php'
	),
	'DVDoug\\BoxPacker\\PackedBox' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/PackedBox.php'
	),
	'DVDoug\\BoxPacker\\PackedBoxList' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/PackedBoxList.php'
	),
	'DVDoug\\BoxPacker\\PackedItem' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/PackedItem.php'
	),
	'DVDoug\\BoxPacker\\PackedItemList' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/PackedItemList.php'
	),
	'DVDoug\\BoxPacker\\PackedLayer' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/PackedLayer.php'
	),
	'DVDoug\\BoxPacker\\Packer' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/Packer.php'
	),
	'DVDoug\\BoxPacker\\Test\\ConstrainedPlacementByCountTestItem' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/ConstrainedPlacementByCountTestItem.php'
	),
	'DVDoug\\BoxPacker\\Test\\ConstrainedPlacementNoStackingTestItem' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/ConstrainedPlacementNoStackingTestItem.php'
	),
	'DVDoug\\BoxPacker\\Test\\ConstrainedTestItem' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/ConstrainedTestItem.php'
	),
	'DVDoug\\BoxPacker\\Test\\LimitedSupplyTestBox' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/LimitedSupplyTestBox.php'
	),
	'DVDoug\\BoxPacker\\Test\\THPackTestItem' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/THPackTestItem.php'
	),
	'DVDoug\\BoxPacker\\Test\\TestBox' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/TestBox.php'
	),
	'DVDoug\\BoxPacker\\Test\\TestItem' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/TestItem.php'
	),
	'DVDoug\\BoxPacker\\VolumePacker' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/VolumePacker.php'
	),
	'DVDoug\\BoxPacker\\WeightRedistributor' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/WeightRedistributor.php'
	),
	'DVDoug\\BoxPacker\\WorkingVolume' => array(
		'version' => '3.9.3.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/WorkingVolume.php'
	),
	'Hook_Manager' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-hook-manager.php'
	),
	'Latest_Autoloader_Guard' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-latest-autoloader-guard.php'
	),
	'Manifest_Reader' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-manifest-reader.php'
	),
	'PHP_Autoloader' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-php-autoloader.php'
	),
	'Path_Processor' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-path-processor.php'
	),
	'Plugin_Locator' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-plugin-locator.php'
	),
	'Plugins_Handler' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-plugins-handler.php'
	),
	'Psr\\Log\\AbstractLogger' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/AbstractLogger.php'
	),
	'Psr\\Log\\InvalidArgumentException' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/InvalidArgumentException.php'
	),
	'Psr\\Log\\LogLevel' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/LogLevel.php'
	),
	'Psr\\Log\\LoggerAwareInterface' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/LoggerAwareInterface.php'
	),
	'Psr\\Log\\LoggerAwareTrait' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/LoggerAwareTrait.php'
	),
	'Psr\\Log\\LoggerInterface' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/LoggerInterface.php'
	),
	'Psr\\Log\\LoggerTrait' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/LoggerTrait.php'
	),
	'Psr\\Log\\NullLogger' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/NullLogger.php'
	),
	'Psr\\Log\\Test\\DummyTest' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/Test/DummyTest.php'
	),
	'Psr\\Log\\Test\\LoggerInterfaceTest' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/Test/LoggerInterfaceTest.php'
	),
	'Psr\\Log\\Test\\TestLogger' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/Test/TestLogger.php'
	),
	'Shutdown_Handler' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-shutdown-handler.php'
	),
	'Version_Loader' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-version-loader.php'
	),
	'Version_Selector' => array(
		'version' => '3.1.3',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-version-selector.php'
	),
	'WooCommerce\\BoxPacker\\Abstract_Item' => array(
		'version' => '1.2.0.0',
		'path'    => $vendorDir . '/woocommerce/box-packer/src/class-abstract-item.php'
	),
	'WooCommerce\\BoxPacker\\Abstract_Packer' => array(
		'version' => '1.2.0.0',
		'path'    => $vendorDir . '/woocommerce/box-packer/src/class-abstract-packer.php'
	),
	'WooCommerce\\BoxPacker\\Box' => array(
		'version' => '1.2.0.0',
		'path'    => $vendorDir . '/woocommerce/box-packer/src/interface-box.php'
	),
	'WooCommerce\\BoxPacker\\DVDoug\\Box' => array(
		'version' => '1.2.0.0',
		'path'    => $vendorDir . '/woocommerce/box-packer/src/dvdoug/class-box.php'
	),
	'WooCommerce\\BoxPacker\\DVDoug\\Item' => array(
		'version' => '1.2.0.0',
		'path'    => $vendorDir . '/woocommerce/box-packer/src/dvdoug/class-item.php'
	),
	'WooCommerce\\BoxPacker\\DVDoug\\Packer' => array(
		'version' => '1.2.0.0',
		'path'    => $vendorDir . '/woocommerce/box-packer/src/dvdoug/class-packer.php'
	),
	'WooCommerce\\BoxPacker\\DVDoug\\Util' => array(
		'version' => '1.2.0.0',
		'path'    => $vendorDir . '/woocommerce/box-packer/src/dvdoug/trait-util.php'
	),
	'WooCommerce\\BoxPacker\\Original\\Box' => array(
		'version' => '1.2.0.0',
		'path'    => $vendorDir . '/woocommerce/box-packer/src/original/class-box.php'
	),
	'WooCommerce\\BoxPacker\\Original\\Item' => array(
		'version' => '1.2.0.0',
		'path'    => $vendorDir . '/woocommerce/box-packer/src/original/class-item.php'
	),
	'WooCommerce\\BoxPacker\\Original\\Packer' => array(
		'version' => '1.2.0.0',
		'path'    => $vendorDir . '/woocommerce/box-packer/src/original/class-packer.php'
	),
	'WooCommerce\\BoxPacker\\Package' => array(
		'version' => '1.2.0.0',
		'path'    => $vendorDir . '/woocommerce/box-packer/src/class-package.php'
	),
	'WooCommerce\\BoxPacker\\WC_Boxpack' => array(
		'version' => '1.2.0.0',
		'path'    => $vendorDir . '/woocommerce/box-packer/src/class-wc-boxpack.php'
	),
);
