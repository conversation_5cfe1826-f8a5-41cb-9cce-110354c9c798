<?php return array(
    'root' => array(
        'name' => 'woocommerce/woocommerce-shipping-royalmail',
        'pretty_version' => 'dev-trunk',
        'version' => 'dev-trunk',
        'reference' => 'b91d8ee05373f71546cb72deb27b8f0393046a86',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'automattic/jetpack-autoloader' => array(
            'pretty_version' => 'v3.1.3',
            'version' => '*******',
            'reference' => 'e7e49a4e2f16cb2dfd3e58c478499a60d7d51839',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../automattic/jetpack-autoloader',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/installers' => array(
            'pretty_version' => 'v1.12.0',
            'version' => '********',
            'reference' => 'd20a64ed3c94748397ff5973488761b22f6d3f19',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/./installers',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dvdoug/boxpacker' => array(
            'pretty_version' => '3.9.3',
            'version' => '3.9.3.0',
            'reference' => 'b9063023729c403e3c20a7635663f29fac569c96',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dvdoug/boxpacker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'roundcube/plugin-installer' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'shama/baton' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'woocommerce/box-packer' => array(
            'pretty_version' => '1.2.0',
            'version' => '*******',
            'reference' => '189c4f91d164516ab38228ea0718608f1940d495',
            'type' => 'library',
            'install_path' => __DIR__ . '/../woocommerce/box-packer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'woocommerce/woocommerce-shipping-royalmail' => array(
            'pretty_version' => 'dev-trunk',
            'version' => 'dev-trunk',
            'reference' => 'b91d8ee05373f71546cb72deb27b8f0393046a86',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
