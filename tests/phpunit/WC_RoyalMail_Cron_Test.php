<?php
/**
 * Test Royal Mail Cron Job functionality.
 *
 * @package WC_RoyalMail
 */

/**
 * Test class for Royal Mail cron job functionality.
 */
class WC_RoyalMail_Cron_Test extends Royalmail_TestCase {

	/**
	 * Test that cron job is scheduled on plugin initialization.
	 */
	public function test_cron_job_scheduled() {
		// Create a new instance to trigger cron scheduling.
		$royalmail = new WC_RoyalMail();
		
		// Trigger the init action to schedule the cron.
		do_action( 'init' );
		
		// Check if the cron job is scheduled.
		$next_scheduled = wp_next_scheduled( 'wc_royalmail_daily_rate_sync' );
		
		$this->assertNotFalse( $next_scheduled, 'Cron job should be scheduled' );
		$this->assertGreaterThan( time(), $next_scheduled, 'Cron job should be scheduled for the future' );
	}

	/**
	 * Test that cron job can be unscheduled.
	 */
	public function test_cron_job_unscheduled() {
		// Create a new instance and schedule the cron.
		$royalmail = new WC_RoyalMail();
		do_action( 'init' );
		
		// Verify it's scheduled.
		$this->assertNotFalse( wp_next_scheduled( 'wc_royalmail_daily_rate_sync' ) );
		
		// Unschedule the cron.
		$royalmail->unschedule_rate_sync_cron();
		
		// Verify it's no longer scheduled.
		$this->assertFalse( wp_next_scheduled( 'wc_royalmail_daily_rate_sync' ), 'Cron job should be unscheduled' );
	}

	/**
	 * Test the daily rate sync callback method exists and is callable.
	 */
	public function test_daily_rate_sync_callback_exists() {
		$royalmail = new WC_RoyalMail();
		
		$this->assertTrue( method_exists( $royalmail, 'daily_rate_sync_callback' ), 'daily_rate_sync_callback method should exist' );
		$this->assertTrue( is_callable( array( $royalmail, 'daily_rate_sync_callback' ) ), 'daily_rate_sync_callback should be callable' );
	}

	/**
	 * Test that the cron callback is properly hooked.
	 */
	public function test_cron_callback_hooked() {
		// Create a new instance to register hooks.
		$royalmail = new WC_RoyalMail();
		
		// Check if the callback is hooked to the cron action.
		$this->assertTrue( has_action( 'wc_royalmail_daily_rate_sync' ), 'Cron callback should be hooked to wc_royalmail_daily_rate_sync action' );
	}

	/**
	 * Test that needs_sync method works correctly.
	 */
	public function test_needs_sync_method() {
		// Ensure the JSON rate loader class is loaded.
		if ( ! class_exists( 'WC_RoyalMail_JSON_Rate_Loader' ) ) {
			include_once WOOCOMMERCE_SHIPPING_ROYALMAIL_ABSPATH . 'includes/class-json-rate-loader.php';
		}
		
		$this->assertTrue( method_exists( 'WC_RoyalMail_JSON_Rate_Loader', 'needs_sync' ), 'needs_sync method should exist' );
		$this->assertTrue( is_callable( array( 'WC_RoyalMail_JSON_Rate_Loader', 'needs_sync' ) ), 'needs_sync should be callable' );
	}

	/**
	 * Test that sync_rates_to_database method works correctly.
	 */
	public function test_sync_rates_to_database_method() {
		// Ensure the JSON rate loader class is loaded.
		if ( ! class_exists( 'WC_RoyalMail_JSON_Rate_Loader' ) ) {
			include_once WOOCOMMERCE_SHIPPING_ROYALMAIL_ABSPATH . 'includes/class-json-rate-loader.php';
		}
		
		$this->assertTrue( method_exists( 'WC_RoyalMail_JSON_Rate_Loader', 'sync_rates_to_database' ), 'sync_rates_to_database method should exist' );
		$this->assertTrue( is_callable( array( 'WC_RoyalMail_JSON_Rate_Loader', 'sync_rates_to_database' ) ), 'sync_rates_to_database should be callable' );
	}

	/**
	 * Clean up after each test.
	 */
	public function tearDown(): void {
		// Clean up any scheduled cron jobs.
		$timestamp = wp_next_scheduled( 'wc_royalmail_daily_rate_sync' );
		if ( $timestamp ) {
			wp_unschedule_event( $timestamp, 'wc_royalmail_daily_rate_sync' );
		}
		
		parent::tearDown();
	}
}
